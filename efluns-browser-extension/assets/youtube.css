.__efluns_median_number{
    border-radius: 10px;
    color: #fff;
    padding: 0 5px;
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 13px;
    cursor: help;
}

/* 烂 */
 .__efluns_median_number[data-type="bad"]{
    background-color: #34AEFF;
}

/* 普通，无色 */
 .__efluns_median_number[data-type="normal"]{
    background-color: transparent;
}

/* 好 */
 .__efluns_median_number[data-type="good"]{
    background-color: #FE2C55;
}



/* 添加提示框样式 */
.__efluns_median_number:hover:after {
    content: "Data from Views ÷ Median Views, via EasyKOL";
    position: absolute;
    bottom: 100%;  /* 位于数字上方 */
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    margin-bottom: 5px;
    z-index: 1000;
}

/* 添加小三角形 */
.__efluns_median_number:hover:before {
    content: "";
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
    margin-bottom: -5px;
    z-index: 1000;
}

a#thumbnail:has(.__efluns_median_number){
    overflow: visible !important;
}

[page-subtype='hashtag-landing-page'] #page-header-container yt-dynamic-text-view-model:has(#efluns-youtube-tag-entry){
    display: flex;
    align-items: center;
    gap: 10px;
}
[page-subtype='hashtag-landing-page'] #page-header-container yt-dynamic-text-view-model #efluns-youtube-tag-entry{
    display: flex;
}


        /* 选择框容器 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-container {
            position: absolute;
            bottom: 84px;
            left: 8px;
            z-index: 10;
        }

        /* 隐藏原生checkbox */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-checkbox {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }

        /* 自定义选择框标签 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
        }

        /* 基础选择框样式 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-box {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.7);
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            position: relative;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        /* 选中状态的选择框 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-checkbox:checked + .ek_similar_from__selection-label .ek_similar_from__selection-box {
            background: #000;
            border-color: #000;
        }

        /* 选中状态的勾号 - 使用伪元素 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-checkbox:checked + .ek_similar_from__selection-label .ek_similar_from__selection-box::after {
            content: '';
            position: absolute;
            left: 6px;
            top: 2px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        /* "Similar from" 文本 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-text {
            background: #000;
            color: white;
            padding: 2px 8px 2px 4px;
            border-radius: 0 12px 12px 0;
            font-size: 10px;
            margin-left: -2px;
            opacity: 0;
            transform: translateX(-10px);
            transition: all 0.2s ease;
        }

        /* 选中时显示文本 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-checkbox:checked + .ek_similar_from__selection-label .ek_similar_from__selection-text {
            opacity: 1;
            transform: translateX(0);
        }

        /* 选中时整个标签的样式调整 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-checkbox:checked + .ek_similar_from__selection-label {
            background: #000;
            border-radius: 12px;
        }

        /* hover效果 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-label:hover .ek_similar_from__selection-box {
            border-color: #000;
            background: rgba(255, 255, 255, 1);
        }

        /* 选中状态下的hover */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-checkbox:checked + .ek_similar_from__selection-label:hover .ek_similar_from__selection-box {
            background: #333;
        }

        /* 焦点状态 */
#contents .ytd-rich-grid-renderer .ek_similar_from__selection-checkbox:focus + .ek_similar_from__selection-label .ek_similar_from__selection-box {
            box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.3);
        }


#contents[similar_from_videos] ytd-rich-item-renderer:has(.ek_similar_from__selection-checkbox:checked){
    border: 3px solid #000;
    box-sizing: border-box;
}

#contents .ek_similar_from__selection-container{
    display: none;
}

#contents[similar_from_videos] .ek_similar_from__selection-container{
    display: block;
}