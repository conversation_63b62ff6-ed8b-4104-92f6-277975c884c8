[data-e2e="user-post-item"]:has(.video-search-highlight) {
    border: 3px solid #ffeb3b;
    box-shadow: 0 0 8px rgba(255, 235, 59, 0.5);
    outline-offset: 4px;  /* 控制边框与元素的距离 */

}



[data-e2e="user-post-item"] .__video__stat{
    display: inline-flex;
    align-items: center;
    gap: 1px;
    margin-right: 4px;
    color: #fff;
    font-size: 13px;
    font-weight: 700;
    font-family: -apple-system, "system-ui", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}


[data-e2e=user-post-item-list] [class*="DivCardFooter"]:has(.__video__stat){
    padding-left: 8px !important;
    padding-right: 1px !important;
    padding-bottom: 1px !important;
    height: auto !important;
}
_[data-e2e=user-post-item-list] [class*="DivCardFooter"] ._video__stat_list{
    display: flex;
    align-items: center;
    gap: 5px;
}

[data-e2e=user-post-item-list] [class*="DivCardFooter"]:has(.__video__stat) > svg , [data-e2e=user-post-item-list] [class*="DivCardFooter"]:has(.__video__stat) > strong{
    display: none;
}

.TUXSegmentedControl{
    position: relative;
}

[data-e2e=user-post-item-list]  .__efluns_created_time{
    position: absolute;
    top: 4px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    font-weight: 700;
    font-size: 13px;
    font-family: -apple-system, "system-ui", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    text-align: center;
}

[data-e2e=user-post-item-list]  .__efluns_duraction{
    position: absolute;
    top: 4px;
    right: 11px;
    color: #fff;
    font-weight: 700;
    font-size: 13px;
    font-family: -apple-system, "system-ui", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

[data-e2e="follow-info-popup"]>div:nth-child(2)>div .efluns-hover-container:hover{
    background-color: #F9FAFB !important;
    filter: drop-shadow(0 4px 6px rgba(0,0,0,0.1)) !important;
}

#efluns-avatar-card-anchor{
    z-index: 99;
}

[data-e2e="user-post-item"] .__efluns_median_number{
    border-radius: 10px;
    color: #fff;
    padding: 0 5px;
    position: relative;
    cursor: help;
}

/* 烂 */
[data-e2e="user-post-item"] .__efluns_median_number[data-type="bad"]{
    background-color: #34AEFF;
}

/* 普通，无色 */
[data-e2e="user-post-item"] .__efluns_median_number[data-type="normal"]{
    background-color: transparent;
}

/* 好 */
[data-e2e="user-post-item"] .__efluns_median_number[data-type="good"]{
    background-color: #FE2C55;
}




/* 添加提示框样式 */
[data-e2e="user-post-item"] .__efluns_median_number:hover:after {
    content: "This post views ÷ Median views\A Red: viral\A Blue: flop";
    text-align: left;
    position: absolute;
    top: 100%;
    width: 180px;
    white-space: pre;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 10px;
    margin-top: 5px;
    z-index: 1000;
}

/* 添加小三角形 */
[data-e2e="user-post-item"] .__efluns_median_number:hover:before {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-bottom-color: rgba(0, 0, 0, 0.8);
    margin-top: -5px;
    z-index: 1000;
}

[data-e2e="user-post-item"] [class*="DivHeaderContainer"]{
    pointer-events: none;
}

[data-e2e="challenge-item"] .__efluns_tag_item{
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 13px;
    font-weight: 700;
    font-family: -apple-system, "system-ui", "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

[data-e2e="challenge-item"] .__efluns_tag_item .__video__stat{
    display: flex;
    align-items: center;
    gap: 3px;
}

#efluns-message-anchor{
    display: none;
}
a[href*='messages?']:hover #efluns-message-anchor{
    display: block;
}

div[class*="DivShareLinks"]:has(#easy-kol-collector-social-links-card){
    a{display: none;}
}

.__efluns_post_tags{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    margin-top: 7px;
}